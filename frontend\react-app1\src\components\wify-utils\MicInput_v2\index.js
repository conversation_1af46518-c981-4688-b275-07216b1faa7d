import {
    AudioOutlined,
    CiCircleFilled,
    CloseOutlined,
    DeleteFilled,
    Dot<PERSON>hartOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Button, Col, message, Progress, Row } from 'antd';
import React, { Component } from 'react';
import Timer, { possibleStates } from '../timer';
import { getModfiedUrlFrFilePreview } from '../S3Uploader/S3Uploader';
import { UploadToS3AndGetMediaPath } from '../UploadToS3AndGetMediaPath';
import { areArraysSame } from '../../../util/helpers';
import { isInsideMobileApp } from '../../../util/AppHelpers';

import ExpandableBox from '../ExpandableBox';
import GaiAudioTranscript from './GaiAudioTranscript';

const helpForAudioPermissionUrl =
    'https://support.google.com/chrome/answer/142065?hl=en&co=GENIE.Platform%3DAndroid';

export default class MicInputV2 extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showPermissionError: false,
            isPermissionDenied: false,
            isRecording: false,
            runTimer: false,
            recorderInstance: undefined,
            // initialFiles:[],
            uploadedFiles: this.props.initialFiles || [],
            newFilesWithStatus: {},
            readyStatus: true,
        };
    }

    haveFilesChanged() {
        let initialFiles = this.props.initialFiles || [];
        // console.log('haveFilesChanged',initialFiles,this.state.uploadedFiles)
        return !areArraysSame(initialFiles, this.state.uploadedFiles);
    }

    getFinalFilesArray() {
        return [...this.state.uploadedFiles];
    }

    async onMicButtonClick() {
        // check permission
        // if yes
        //  check if recording is on
        //      no stop the recording
        //      yes start the recording
        // if no
        //      Check if firt time ?
        //          yes -> trigger browser options
        //          no -> is blocked ?
        //                  yes -> show blocked message
        //                  no -> tirgger browser option
        let currentMicPermissionState;
        try {
            if (isInsideMobileApp()) {
                currentMicPermissionState = 'granted';
            } else {
                currentMicPermissionState = (await this.getMicPermissionState())
                    ?.state;
            }
        } catch (error) {
            console.log('Error getting permission', error);
            message.error('Error getting permission for mic');
        }
        if (currentMicPermissionState == 'granted') {
            this.toggelRecordingState();
        } else {
            if (currentMicPermissionState == 'prompt') {
                // first time or not yet denied
                this.triggerBrowserPermissionRequest();
            } else if (currentMicPermissionState == 'denied') {
                this.showDeniedPermissionError();
            }
        }
    }

    getTimerValue() {
        const timerState = this.state.runTimer
            ? possibleStates.START
            : possibleStates.RESET;
        return <Timer timer_state={timerState} />;
    }

    toggelRecordingState() {
        this.setState({
            showPermissionError: false,
            isPermissionDenied: false,
        });
        // check if recording is already on
        // yes
        //  stop the recording,
        // no
        //  start the recording, start the timer
        if (this.state.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    onNewRecording(url) {
        // add this recording to new files array
        const currentFilesVsStatusObj = { ...this.state.newFilesWithStatus };
        currentFilesVsStatusObj[url] = 0;
        this.setState(
            {
                newFilesWithStatus: currentFilesVsStatusObj,
            },
            () => {
                this.updateReadyStatus();
            }
        );
        // start upload of this file
        UploadToS3AndGetMediaPath(
            url,
            'audio_record.mp3',
            (progress, uploadedUrl) => {
                const currentFilesVsStatusObj = {
                    ...this.state.newFilesWithStatus,
                };
                currentFilesVsStatusObj[url] = progress;
                this.setState(
                    {
                        newFilesWithStatus: currentFilesVsStatusObj,
                    },
                    () => {
                        this.updateReadyStatus();
                    }
                );
                if (progress == 100) {
                    if (!this.state.uploadedFiles.includes(uploadedUrl)) {
                        this.setState(
                            {
                                uploadedFiles: [
                                    ...this.state.uploadedFiles,
                                    uploadedUrl,
                                ],
                            },
                            () => this.tellParentFilesChanged()
                        );
                    }
                }
            }
        );
    }

    tellParentFilesChanged() {
        if (this.props.onFilesChanged) {
            this.props.onFilesChanged(this.getFinalFilesArray());
        }
    }

    updateReadyStatus() {
        const lessThan100Files = [];
        Object.keys(this.state.newFilesWithStatus).forEach((singleNewFile) => {
            if (this.state.newFilesWithStatus[singleNewFile] != 100) {
                lessThan100Files.push(singleNewFile);
            }
        });
        this.setState(
            {
                readyStatus:
                    !this.state.isRecording && lessThan100Files.length == 0,
            },
            () => {
                this.updateReadyStatusToParent();
            }
        );
    }

    updateReadyStatusToParent() {
        if (this.props.onReadyStatusChange) {
            this.props.onReadyStatusChange(this.state.readyStatus);
        }
    }

    onRecordingCompleted = (e) => {
        this.onNewRecording(URL.createObjectURL(e.data));
    };

    async startRecording() {
        this.setState(
            {
                isRecording: true,
            },
            () => this.updateReadyStatus()
        );
        // start the recording, start the timer
        const recorder = await this.getRecorder();
        if (recorder) {
            // Obtain the audio when ready.
            recorder.addEventListener('dataavailable', (e) =>
                this.onRecordingCompleted(e)
            );
            recorder.start();
            this.setState({
                recorderInstance: recorder,
                runTimer: true,
            });
        } else {
            this.showPermissionError();
            this.setState({
                isRecording: false,
            });
        }
    }

    async stopRecording() {
        // reset the timer, update preview
        this.setState({
            isRecording: false,
            runTimer: false,
        });
        if (this.state.recorderInstance) {
            this.state.recorderInstance.stop();

            this.setState({
                recorderInstance: undefined,
            });
        }
    }

    async getRecorder() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true,
            });
            return new MediaRecorder(stream);
        } catch (err) {
            return undefined;
        }
    }

    triggerBrowserPermissionRequest() {
        navigator.mediaDevices
            .getUserMedia({ video: false, audio: true })
            .then((stream) => {
                this.onMicButtonClick();
                window.localStream = stream;
                window.localAudio.srcObject = stream;
                window.localAudio.autoplay = true;
            })
            .catch((err) => {
                console.log(err);
                this.showPermissionError();
            });
    }

    showPermissionError() {
        this.setState({
            showPermissionError: true,
        });
    }

    showDeniedPermissionError() {
        this.setState({
            showPermissionError: true,
            isPermissionDenied: true,
        });
    }

    async getMicPermissionState() {
        return await navigator.permissions.query({ name: 'microphone' });
    }

    async requestRecorder() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true,
            });
            return new MediaRecorder(stream);
        } catch (err) {
            console.log(err);
            return new MediaError(err);
        }
    }

    getModifiedUrlToPReview(uploadedFileUrl) {
        return getModfiedUrlFrFilePreview(this.props, uploadedFileUrl);
    }

    deleteFileFromUploadedFiles(index) {
        const tempUploadedFiles = [...this.state.uploadedFiles];
        tempUploadedFiles.splice(index, 1);
        this.setState(
            {
                uploadedFiles: tempUploadedFiles,
            },
            () => this.tellParentFilesChanged()
        );
    }

    audioTranscriptUI({ singleFileUrl, index }) {
        const selectedAudioTranscript =
            this.props?.gaiAudioTranscript?.data?.[singleFileUrl];
        const defaultState = this.props?.gaiAudioTranscript?.defaultState;
        if (
            !selectedAudioTranscript ||
            Object.keys(selectedAudioTranscript).length === 0
        )
            return <></>;

        return (
            <div data-testid="gai-audio-transcript">
                <GaiAudioTranscript
                    audioTranscript={selectedAudioTranscript}
                    defaultState={{
                        isOpen: defaultState?.isOpen,
                        openLabel: defaultState?.openLabel,
                        closeLabel: defaultState?.closeLabel,
                    }}
                />
            </div>
        );
    }

    render() {
        const {
            showPermissionError,
            isPermissionDenied,
            newFilesWithStatus,
            uploadedFiles,
            isRecording,
        } = this.state;
        return (
            <>
                <div
                    {...(!this.props.readOnly
                        ? { style: { border: '2px dashed #d4d4d4' } }
                        : {})}
                >
                    {this.haveFilesChanged() && (
                        <Alert
                            className="gx-m-3"
                            message="**Unsaved changes"
                            type="warning"
                            showIcon
                        />
                    )}
                    {!this.props.readOnly && (
                        <div className="gx-d-flex gx-align-items-sm-center gx-p-3 ">
                            <Button
                                icon={
                                    isRecording ? (
                                        <p className="gx-mb-0 gx-ml-lg-2">
                                            <span className="icon icon-circle gx-module-box"></span>
                                        </p>
                                    ) : (
                                        <AudioOutlined />
                                    )
                                }
                                onClick={(e) => {
                                    this.onMicButtonClick();
                                }}
                                // type="primary"
                                className={`gx-mr-2 gx-mb-0 ${isRecording ? 'gx-bg-red' : 'gx-bg-primary'} gx-text-white`}
                            />
                            <div>{this.getTimerValue()}</div>
                        </div>
                    )}
                    <Row className="gx-px-3">
                        {Object.keys(newFilesWithStatus).map(
                            (singleNewFile, index) => {
                                if (newFilesWithStatus[singleNewFile] != 100) {
                                    return (
                                        <Col key={index} xs={24} lg={6}>
                                            {`Audio ${index + 1}`}
                                            <Progress
                                                percent={
                                                    newFilesWithStatus[
                                                        singleNewFile
                                                    ]
                                                }
                                                status="active"
                                            />
                                        </Col>
                                    );
                                }
                            }
                        )}
                    </Row>

                    {this.getFinalFilesArray().map((singleFileUrl, index) => {
                        let modifiedUrl =
                            this.getModifiedUrlToPReview(singleFileUrl);
                        return (
                            <div className="wy-audio-wrapper gx-px-3 gx-py-1 gx-mb-2">
                                <audio
                                    src={modifiedUrl}
                                    controls
                                    className="gx-w-100"
                                />
                                {!this.props.readOnly && (
                                    <span className="gx-fs-11">
                                        {/* YOu should ask as a popup first are you sure                             */}
                                        <CloseOutlined
                                            className="wy-audio-del gx-text-white"
                                            onClick={() => {
                                                this.deleteFileFromUploadedFiles(
                                                    index
                                                );
                                            }}
                                        />
                                    </span>
                                )}
                                {this.audioTranscriptUI({
                                    singleFileUrl,
                                    index,
                                })}
                            </div>
                        );
                    })}
                </div>

                {showPermissionError && (
                    <div className="gx-text-red">
                        Oops!!, unable to get audio permission,
                        {!isPermissionDenied && (
                            <a onClick={() => this.onMicButtonClick()}>
                                {' '}
                                Retry,
                            </a>
                        )}
                        <a href={helpForAudioPermissionUrl} target="_blank">
                            {' '}
                            Know more
                        </a>
                    </div>
                )}
            </>
        );
    }
}
