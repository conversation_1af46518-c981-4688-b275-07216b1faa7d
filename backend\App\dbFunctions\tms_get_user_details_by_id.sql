CREATE OR REPLACE FUNCTION public.tms_get_user_details_by_id(entry_id uuid, show_inactive bool)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare 
		status boolean;
		message text;
		resp_data json;
	BEGIN
		status = false;
	message = 'Internal_error';
	resp_data = array_to_json(array(
			select jsonb_build_object(
					'uuid',users.usr_id,
					'name',
					CASE 
						WHEN show_inactive THEN 
							CASE 
								WHEN users.is_active THEN users."name"
								ELSE users."name" || ' (inactive)'
							END
						ELSE users."name"
					END,
					'email',usr_email.ind_id ,
					'mobile', usr_mobile.ind_id ,
					'designation',users.designation 
				   )
		      from public.cl_tx_users as users		     		  
		     inner join cl_tx_usr_identities as usr_email
		        on users.usr_id = usr_email.user_id 
		       and usr_email.ind_type = 'EMAIL'
		      left join cl_tx_usr_identities as usr_mobile
		        on users.usr_id = usr_mobile.user_id 
		       and usr_mobile.ind_type = 'MOBILE_NUM'
			 where users.usr_id = entry_id
		     group by usr_email.id ,users.usr_id, usr_mobile.id
		)
	);
	
	if json_array_length(resp_data) > 0 then 
		status = true;
		message = 'user_found';
		
	end if;
	return jsonb_build_object('status',status,'code',message,'data',resp_data->0);
	END;
$function$
;
