import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Image, message, Progress, Row, Spin } from 'antd';
import React, { Component } from 'react';
import {
    CameraOutlined,
    CheckOutlined,
    CloseOutlined,
    ConsoleSqlOutlined,
    DeleteOutlined,
    RedoOutlined,
    SendOutlined,
    VideoCameraFilled,
} from '@ant-design/icons';
import Webcam from 'react-webcam';
import Camera from 'react-html5-camera-photo'; // For android devices
import 'react-html5-camera-photo/build/css/index.css';

import S3Uploader, {
    getModfiedUrlFrFilePreview,
} from '../S3Uploader/S3Uploader';
import { UploadToS3AndGetMediaPath } from '../UploadToS3AndGetMediaPath';
import { areArraysSame } from '../../../util/helpers';
import { isInsideMobileApp } from '../../../util/AppHelpers';

const helpForAudioPermissionUrl =
    'https://support.google.com/chrome/answer/142065?hl=en&co=GENIE.Platform%3DAndroid';

const videoConstraints = {
    width: 250,
    height: 250,
    facingMode: 'user',
};

export default class CameraInput extends Component {
    constructor(props) {
        super(props);
        this.camRef = React.createRef();
    }
    initState = {
        showPermissionError: false,
        isPermissionDenied: false,
        isCapturing: false,
        recorderInstance: undefined,
        streamInstance: undefined,
        imgSrc: undefined,
        reTake: false,
        upload: false,
        uploadedFiles: this.props.initialFiles || [],
        newFilesWithStatus: {},
        readyStatus: true,
        loading: false,
        openTheCameraNow: true,
        loadingCameraInput: false,
    };
    state = this.initState;

    componentDidMount() {
        if (this.props.readOnly == undefined || !this.props.readOnly) {
            this.initializeCamera();
        }
    }

    useHtml5CameraPhoto() {
        return (
            isInsideMobileApp() &&
            process.env.REACT_USE_HTML5_CAMERA_FR_MOBILE_APP
        );
    }

    initializeCamera() {
        if (this.useHtml5CameraPhoto()) {
            this.setState({
                openTheCameraNow: true,
                loadingCameraInput: false,
            });
        } else {
            this.setState(
                {
                    openTheCameraNow: false,
                    loadingCameraInput: true,
                },
                async () => {
                    const recorder = await this.getRecorder();
                    if (recorder) {
                        console.log('recorder', recorder);
                        recorder.stream.addEventListener('inactive', (e) => {
                            console.log('recorder inactive');
                            this.setState({
                                openTheCameraNow: true,
                                loadingCameraInput: false,
                            });
                        });
                        recorder.stream
                            .getTracks()
                            .map((track) => track.stop());
                    } else {
                        this.showPermissionError();
                    }
                }
            );
        }
    }

    async onButtonClick() {
        let currentCameraPermissionState;
        try {
            if (this.useHtml5CameraPhoto()) {
                currentCameraPermissionState = 'granted';
                this.showLoaderForAndroidCamera();
            } else {
                currentCameraPermissionState = (
                    await this.getCameraPermissionState()
                )?.state;
            }
        } catch (error) {
            console.log('Error getting permission', error);
            message.error('Error getting permission for camera');
        }
        if (currentCameraPermissionState == 'granted') {
            this.onCapturingState();
        } else {
            if (currentCameraPermissionState == 'prompt') {
                // first time or not yet denied
                // console.log('coming')
                this.triggerBrowserPermissionRequest();
            } else if (currentCameraPermissionState == 'denied') {
                this.showDeniedPermissionError();
            }
        }
    }
    onCapturingCompleted = (e) => {
        this.onNewCapturing(this.state.imgSrc);
    };

    onNewCapturing(url) {
        // add this recording to new files array
        if (url != undefined) {
            const currentFilesVsStatusObj = {
                ...this.state.newFilesWithStatus,
            };
            currentFilesVsStatusObj[url] = 0;
            this.setState(
                {
                    newFilesWithStatus: currentFilesVsStatusObj,
                },
                () => {
                    this.updateReadyStatus();
                }
            );
            // start upload of this file
            UploadToS3AndGetMediaPath(
                url,
                'capture_image.png',
                (progress, uploadedUrl, uploadCompleted) => {
                    const currentFilesVsStatusObj = {
                        ...this.state.newFilesWithStatus,
                    };
                    currentFilesVsStatusObj[url] = progress;
                    this.setState(
                        {
                            newFilesWithStatus: currentFilesVsStatusObj,
                        },
                        () => {
                            this.updateReadyStatus();
                        }
                    );
                    if (progress == 100 && uploadCompleted) {
                        if (!this.state.uploadedFiles.includes(uploadedUrl)) {
                            this.setState(
                                {
                                    uploadedFiles: [
                                        ...this.state.uploadedFiles,
                                        uploadedUrl,
                                    ],
                                },
                                () => this.tellParentFilesChanged()
                            );
                        }
                    }
                }
            );
        }
        this.setState({
            imgSrc: undefined,
        });
    }

    updateReadyStatus() {
        const lessThan100Files = [];
        Object.keys(this.state.newFilesWithStatus).forEach((singleNewFile) => {
            if (this.state.newFilesWithStatus[singleNewFile] != 100) {
                lessThan100Files.push(singleNewFile);
            }
        });
        this.setState(
            {
                readyStatus:
                    !this.state.isCapturing && lessThan100Files.length == 0,
            },
            () => {
                this.updateReadyStatusToParent();
            }
        );
    }

    updateReadyStatusToParent() {
        if (this.props.onReadyStatusChange) {
            this.props.onReadyStatusChange(this.state.readyStatus);
        }
    }

    async startCapturing() {
        this.setState(
            {
                isCapturing: true,
            },
            () => this.updateReadyStatus()
        );
        // start the capturing
        const recorder = await this.getRecorder();
        if (recorder) {
            recorder.addEventListener(
                'dataavailable',
                (e) => this.onCapturingCompleted(e) // this gets called in bg when clicked on captured
            );
            recorder.start();
            this.setState({
                recorderInstance: recorder,
            });
        } else {
            this.showPermissionError();
            this.setState({
                isCapturing: false,
            });
        }
    }

    async stopCapturing() {
        this.setState({
            isCapturing: false,
        });
        //stop capturing
        if (this.state.recorderInstance) {
            this.state.recorderInstance.stop();
            this.state.streamInstance
                .getTracks()
                .forEach((track) => track.stop());
            this.setState({
                recorderInstance: undefined,
                newFilesWithStatus: {},
                reTake: false,
            });
        }
    }

    onCapturingState() {
        this.setState({
            showPermissionError: false,
            isPermissionDenied: false,
        });
        this.startCapturing();
    }

    async getRecorder() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
            });
            this.setState({
                streamInstance: stream,
            });
            return new MediaRecorder(stream);
        } catch (err) {
            console.error(err);
            return undefined;
        }
    }

    triggerBrowserPermissionRequest() {
        navigator.mediaDevices
            .getUserMedia({ video: true })
            .then((stream) => {
                this.onButtonClick();
                //this.camRef.current.srcObject=stream
                //window.localStream = stream;
                window.localVideo.srcObject = stream; //after permssion camera will take some secs to off
                window.localVideo.autoplay = true;
            })
            .catch((err) => {
                console.log(err);
                this.showPermissionError();
            });
    }

    showPermissionError() {
        this.setState({
            showPermissionError: true,
            loadingCameraInput: false,
        });
    }

    showDeniedPermissionError() {
        this.setState({
            showPermissionError: true,
            isPermissionDenied: true,
        });
    }

    async getCameraPermissionState() {
        return await navigator.permissions.query({ name: 'camera' });
    }

    onFilesChanged(section, files) {
        let newFilesBySection = this.state.filesBySection;
        newFilesBySection[section] = files;
        this.setState({
            filesBySection: newFilesBySection,
        });
    }

    onFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({
            sectionWiseUploaderReady: newSectionWiseReady,
        });
    }

    haveFilesChanged() {
        let initialFiles = this.props.initialFiles || [];
        // console.log('haveFilesChanged',initialFiles,this.state.uploadedFiles)
        return !areArraysSame(initialFiles, this.state.uploadedFiles);
    }

    getFinalFilesArray() {
        return [...this.state.uploadedFiles];
    }

    getModifiedUrlToPReview(uploadedFileUrl) {
        return getModfiedUrlFrFilePreview(this.props, uploadedFileUrl);
    }

    deleteFileFromUploadedFiles(index) {
        const tempUploadedFiles = [...this.state.uploadedFiles];
        tempUploadedFiles.splice(index, 1);
        this.setState(
            {
                uploadedFiles: tempUploadedFiles,
            },
            () => this.tellParentFilesChanged()
        );
    }

    tellParentFilesChanged() {
        if (this.props.onFilesChanged) {
            this.props.onFilesChanged(this.getFinalFilesArray());
        }
    }

    closeCamera() {
        if (this.useHtml5CameraPhoto()) {
            // Stop camera access when closed
            this.handleCameraStop();
        }
        this.stopCapturing();
        if (this.props.onReadyStatusChange) {
            this.props.onReadyStatusChange(true);
        }
    }

    // Below 4 methods are used for Androd camera interaction
    handleTakePhoto(dataUri) {
        this.setState({
            imgSrc: dataUri,
            reTake: true,
        });
    }

    handleCameraStart = (stream) => {
        this.streamInstance = stream;
        setTimeout(() => {
            this.setState({
                loading: false,
            });
        }, 500);
    };

    handleCameraStop = () => {
        this.setState({
            loading: true,
        });

        if (this.streamInstance) {
            // Stop all tracks of the stream
            this.streamInstance.getTracks().forEach((track) => track.stop());
            this.streamInstance = null; // Clear the stored stream reference
        }
    };
    handleCameraError(error) {
        this.showPermissionError();
    }
    showLoaderForAndroidCamera() {
        this.setState({
            loading: true,
        });
    }
    render() {
        const {
            isCapturing,
            showPermissionError,
            uploadedFiles,
            isPermissionDenied,
            imgSrc,
            newFilesWithStatus,
            openTheCameraNow,
            loadingCameraInput,
        } = this.state;

        return (
            <>
                <div>
                    {this.haveFilesChanged() && (
                        <Alert
                            className="gx-m-3"
                            message="**Unsaved changes"
                            type="warning"
                            showIcon
                        />
                    )}
                    {!this.props.readOnly && (
                        <div className="gx-d-flex gx-align-items-sm-center gx-p-3 ">
                            {openTheCameraNow && !isCapturing ? (
                                <Button
                                    data-testid="openCameraButton"
                                    icon={
                                        isCapturing ? (
                                            <p className="gx-mb-0 gx-ml-lg-2">
                                                <span className="icon icon-circle gx-module-box"></span>
                                            </p>
                                        ) : (
                                            <CameraOutlined />
                                        )
                                    }
                                    onClick={(e) => {
                                        this.onButtonClick();
                                    }}
                                    className={`gx-mr-2 gx-mb-0 ${
                                        isCapturing
                                            ? 'gx-bg-red'
                                            : 'gx-bg-primary'
                                    } gx-text-white`}
                                />
                            ) : (
                                <Spin spinning={loadingCameraInput} />
                            )}
                            {isCapturing && (
                                <div className="gx-position-relative">
                                    {!this.state.reTake && (
                                        <Spin
                                            tip={'Please wait ...'}
                                            spinning={this.state.loading}
                                        >
                                            <div className="wy-camera-input-wrapper gx-position-relative">
                                                {!isInsideMobileApp() ? (
                                                    <>
                                                        <Webcam
                                                            ref={this.camRef}
                                                            audio={false}
                                                            height={250}
                                                            width={250}
                                                            videoConstraints={
                                                                videoConstraints
                                                            }
                                                        ></Webcam>
                                                        <Button
                                                            className="gx-mb-0 gx-btn-orange"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                this.setState({
                                                                    loading: true,
                                                                });
                                                                // as this.camRef.current.getScreenshot blocks the ui thread
                                                                setTimeout(
                                                                    () => {
                                                                        this.setState(
                                                                            {
                                                                                imgSrc: this.camRef.current.getScreenshot(
                                                                                    {
                                                                                        width: 4896,
                                                                                        height: 6528,
                                                                                    }
                                                                                ),
                                                                                reTake: true,
                                                                            }
                                                                        );
                                                                    },
                                                                    200
                                                                );
                                                            }}
                                                        >
                                                            <CameraOutlined />{' '}
                                                            Capture photo
                                                        </Button>
                                                    </>
                                                ) : (
                                                    // For Android
                                                    <Camera
                                                        style={{
                                                            width: '100%',
                                                            height: '500px',
                                                        }}
                                                        imageCompression={0.9}
                                                        onTakePhoto={(
                                                            dataUri
                                                        ) => {
                                                            this.handleTakePhoto(
                                                                dataUri
                                                            );
                                                        }}
                                                        onCameraStart={(
                                                            stream
                                                        ) => {
                                                            this.handleCameraStart(
                                                                stream
                                                            );
                                                        }}
                                                        onCameraStop={() => {
                                                            this.handleCameraStop();
                                                        }}
                                                        onCameraError={(
                                                            error
                                                        ) => {
                                                            this.handleCameraError(
                                                                error
                                                            );
                                                        }}
                                                    />
                                                )}
                                                <CloseOutlined
                                                    className="wy-audio-del gx-text-white wy-camera-close-icon"
                                                    onClick={() => {
                                                        this.closeCamera();
                                                    }}
                                                />
                                            </div>
                                        </Spin>
                                    )}

                                    {imgSrc != undefined && (
                                        <img
                                            data-testid="capturedImg"
                                            src={this.state.imgSrc}
                                            width={250}
                                            height={250}
                                        />
                                    )}

                                    {this.state.reTake && (
                                        <div className="wy-retake-wrapper gx-d-flex">
                                            <Button
                                                className="gx-mb-0 gx-btn-outline-info"
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    this.setState({
                                                        loading: false,
                                                        imgSrc: undefined,
                                                        reTake: false,
                                                    });
                                                }}
                                            >
                                                <RedoOutlined /> Retake
                                            </Button>
                                            <Button
                                                data-testid="saveImage"
                                                className="gx-mb-0 gx-btn-success"
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    this.setState({
                                                        loading: false,
                                                    });
                                                    this.stopCapturing();
                                                }}
                                            >
                                                <CheckOutlined /> Save
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    )}
                    <Row className="gx-px-3">
                        {Object.keys(newFilesWithStatus).map(
                            (singleNewFile, index) => {
                                if (newFilesWithStatus[singleNewFile] != 100) {
                                    return (
                                        <Col
                                            key={index}
                                            xs={24}
                                            lg={6}
                                            data-testid="imgUploadProgress"
                                        >
                                            {`Image ${index + 1}`}
                                            <Progress
                                                percent={
                                                    newFilesWithStatus[
                                                        singleNewFile
                                                    ]
                                                }
                                                status="active"
                                            />
                                        </Col>
                                    );
                                }
                            }
                        )}
                    </Row>

                    {this.getFinalFilesArray().map((singleFileUrl, index) => {
                        let modifiedUrl =
                            this.getModifiedUrlToPReview(singleFileUrl);
                        return (
                            <div
                                data-testid="capturedPreview"
                                className="wy-audio-wrapper gx-px-3 gx-py-1 gx-mb-2 gx-d-inline-block"
                            >
                                <img
                                    src={modifiedUrl}
                                    width={250}
                                    height={250}
                                />
                                {!this.props.readOnly && (
                                    <span className="gx-fs-11">
                                        {/* YOu should ask as a popup first are you sure                             */}
                                        <CloseOutlined
                                            data-testid="closePreviewImg"
                                            className="wy-audio-del gx-text-white wy-camera-close-icon"
                                            onClick={() => {
                                                this.deleteFileFromUploadedFiles(
                                                    index
                                                );
                                            }}
                                        />
                                    </span>
                                )}
                            </div>
                        );
                    })}
                </div>
                {showPermissionError && (
                    <div className="gx-text-red">
                        Oops!!, unable to get camera permission,
                        {!isPermissionDenied && (
                            <a onClick={() => this.onButtonClick()}> Retry,</a>
                        )}
                        <a href={helpForAudioPermissionUrl} target="_blank">
                            {' '}
                            Know more
                        </a>
                    </div>
                )}
                <input
                    value={
                        this.state.uploadedFiles.length > 0
                            ? 'file_present'
                            : ''
                    }
                    type={'text'}
                    required={this.props.required}
                    style={{ height: 0, opacity: 0 }}
                    readOnly
                />
            </>
        );
    }
}
