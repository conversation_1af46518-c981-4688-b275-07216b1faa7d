CREATE OR REPLACE FUNCTION public.tms_hlpr_clear_booked_slots_from_form_data(srvc_req_id_ integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
    status boolean;
    message text;
    resp_data json;
    affected_rows integer;
    existing_form_data jsonb;
begin
    status = false;
    message = 'Internal_error';
    
    -- Get existing form_data
    SELECT form_data
    FROM cl_tx_srvc_req
    WHERE db_id = srvc_req_id_
    INTO existing_form_data;
    
    if existing_form_data is not null then
        -- Remove booked_slots from form_data
        UPDATE public.cl_tx_srvc_req 
          SET form_data = jsonb_set(existing_form_data - 'booked_slots' - 'start_time'- 'end_time' - 'request_req_date', '{booking_data}', 'null'::jsonb,true),
           capacity_id = NULL  ,
           booking_details = null
        WHERE db_id = srvc_req_id_;
        
        GET DIAGNOSTICS affected_rows = ROW_COUNT;
        
        if affected_rows > 0 then
            status = true;
            message = 'success';
            resp_data = json_build_object(
                'srvc_req_id', srvc_req_id_,
                'booked_slots_cleared', true
            );
        end if;
    else
        -- Service request not found, but we'll consider it success
        status = true;
        message = 'service_request_not_found';
        resp_data = json_build_object(
            'srvc_req_id', srvc_req_id_,
            'booked_slots_cleared', false
        );
    end if;
    
    return json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
