.ant-drawer-right.ant-drawer-open.wy-acb-drawer-wrapper
    .ant-drawer-content-wrapper {
    width: 100% !important;
    padding: 0 !important;
    max-width: 550px;
}

@media screen and (max-width: 991px) {
    .ant-drawer-bottom.ant-drawer-open.wy-acb-drawer-wrapper
        .ant-drawer-content-wrapper {
        width: 100% !important;
        padding: 0 !important;
        max-width: 98%;
        border-radius: 10px 10px 0 0;
        overflow: hidden;
        height: 90vh !important;
        margin-left: 1%;
    }
}

.wy-acb-chat-container {
    display: flex;
    flex-direction: column;
    height: 99.8%;
    width: 99.8%;
    align-items: stretch;
    background-color: #ffffff;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 1) 29%,
        rgba(214, 239, 255, 1) 100%
    );
    position: relative;
    z-index: 1;
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
}

.wy-acb-chat-container:before {
    content: '';
    display: block;
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #ffffffd0;
}

.wy-acb-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.wy-acb-message {
    margin-bottom: 12px;
}

.wy-acb-message.assistant {
    text-align: left;
}

.wy-acb-message.user {
    text-align: right;
}

.wy-acb-message-bubble {
    display: inline-block;
    background-color: #dedede;
    padding: 10px 16px;
    border-radius: 16px;
    max-width: 100%;
    word-wrap: break-word;
    color: #454545;
}

.wy-acb-timestamp {
    font-size: 10px;
    color: #94a3b8;
}

.wy-acb-action-buttons {
    padding: 5px;
    display: flex;
    gap: 0.4rem;
}

.wy-acb-action-button {
    background-color: #e6faff;
    border: none;
    color: #404040;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    text-align: left;
    font-size: 12px;
}

.wy-acb-action-button:hover {
    background-color: #afebfa;
}

.wy-acb-prompt-wrapper {
    display: flex;
    align-items: center;
    padding: 10px 10px;
    background-color: #ffffff;
    border-top: 1px solid #eee;
}

.wy-acb-prompt-wrapper input {
    flex: 1 1;
    background: #fff;
    padding: 8px;
    font-size: 14px;
    outline: none;
    border: 1px solid #eee;
    border-radius: 10px;
    color: #000;
}

.wy-acb-send-button {
    border: none;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    margin-left: 8px;
    background: #038fde;
    /* height: 100%; */
    display: flex;
    border-radius: 10px;
    padding: 10px;
}

.wy-acb-message.tms-ai {
    position: relative;
}

.tms-name {
    font-size: 0.85rem;
    color: #007bff;
}

/* HTML: <div class="loader"></div> */
.wy-acb-initial-loading {
    width: 30px;
    aspect-ratio: 2;
    --_g: no-repeat radial-gradient(circle closest-side, #000000 90%, #0000);
    background:
        var(--_g) 0% 50%,
        var(--_g) 50% 50%,
        var(--_g) 100% 50%;
    background-size: calc(100% / 3) 50%;
    animation: l3 1s infinite linear;
}
@keyframes l3 {
    20% {
        background-position:
            0% 0%,
            50% 50%,
            100% 50%;
    }
    40% {
        background-position:
            0% 100%,
            50% 0%,
            100% 50%;
    }
    60% {
        background-position:
            0% 50%,
            50% 100%,
            100% 0%;
    }
    80% {
        background-position:
            0% 50%,
            50% 50%,
            100% 100%;
    }
}

.wy-acb-message-wrapper.ai .wy-acb-message-bubble {
    border-radius: 5px;
    width: 100%;
    background-color: #fff;
    padding: 10px 0;
}

.wy-acb-message-wrapper.user {
    display: flex;
    justify-content: flex-end;
}

.wy-acb-message-wrapper.user .wy-acb-message-bubble {
    border-radius: 10px;
    background: #e9e9e980;
    color: #000000;
}

.wy-acb-chat-header h2 {
    color: #252525;
}
.wy-acb-chat-header {
    text-align: center;
    padding: 10px;
}

.wy-acb-intro-message {
    margin: 10px auto;
    padding: 10px 15px;
    border-radius: 20px;
    display: inline-block;
    font-size: 18px;
    color: #3f3f3f;
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    flex-wrap: wrap;
    height: 100%;
    justify-content: center;
}

.wy-acb-jump-to-latest-button {
    position: absolute;
    bottom: 162px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background-color: #e6faff;
    color: #038fe4;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.6s ease-in-out;
    border: none;
}

.wy-acb-message-wrapper {
    position: relative;
    display: inline-block;
}

.user .wy-acb-message-wrapper:hover .wy-acb-feedback-bubble {
    display: none;
}

.ai .wy-acb-message-bubble {
    margin-bottom: 0px;
}

.ai .wy-acb-message-feedback-wrapper {
    margin-bottom: 30px;
}

.ai .wy-acb-feedback-bubble {
    background: #ffffff;
    padding: 0;
    z-index: 10;
    white-space: nowrap;
    grid-gap: 2px;
    gap: 2px;
    display: flex;
    align-items: center;
    justify-content: start;
    transition: all 1s ease-in-out;
}

.ai .wy-acb-timestamp,
.user .wy-acb-timestamp {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 12px;
    color: #94a3b8;
}

.ai .wy-acb-message-wrapper .wy-acb-feedback-bubble {
    display: flex;
}
.user .wy-acb-message-wrapper .wy-acb-feedback-bubble {
    display: none;
}

.wy-acb-feedback-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    padding: 3px;
    transition: transform 0.2s ease;
}

.wy-acb-feedback-btn svg {
    stroke: black;
}
.wy-acb-feedback-btn.comment svg {
    fill: black;
}

.wy-acb-feedback-btn:hover {
    transform: scale(1.2);
}

.wy-acb-history-wrapper {
    padding: 12px;
}

.wy-acb-chat-history-list {
    max-height: calc(100vh - 14rem);
    overflow-y: auto;
}

.wy-acb-history-switch-button {
    color: #000;
    background-color: #f3f3f3;
}

.wy-acb-history-switch-button:hover {
    background-color: #eee;
    color: #000;
}

@media screen and (max-width: 991px) {
    .wy-acb-chat-history-list {
        max-height: calc(100vh - 20rem);
        overflow-y: auto;
    }
}

textarea.wy-acb-prompt-textarea,
textarea.wy-acb-prompt-textarea:focus-visible {
    width: 100%;
    border: 1px solid #eee;
    padding: 10px;
    resize: none;
}

.wy-acb-new-chat-button {
    background: #038fde;
    border: none;
    color: #ffffff;
}

.wy-acb-new-chat-button:hover {
    background: #ffffff;
    color: #038fde;
}

.wy-acb-history-button {
    border: none;
    color: #038fde;
    font-size: 14px;
}
.wy-acb-history-button:hover {
    border: none;
    color: #fff;
    background: #038fde;
    font-size: 14px;
}

.wy-acb-history-title {
    max-width: 60vw;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

@media screen and (max-width: 991px) {
    .wy-acb-history-title {
        max-width: 60vw;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* background: #000; */
        width: 100%;
    }
    .wy-acb-title-ai-assistant {
        font-size: 16px;
    }
}

.wy-acb-add-attachments {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: 8px;
    background: #e6faff;
    display: flex;
    border-radius: 10px;
    padding: 10px;
    margin-left: 0;
}

.user .wy-acb-feedback-bubble {
    display: none;
}

.user .gx-justify-content-between {
    justify-content: end !important;
}

.wy-acb-name{
    text-align: center;
    width: 100%;
    font-size: 30px;
    margin-bottom: 15px;
}