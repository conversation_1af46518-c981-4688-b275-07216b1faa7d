import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CameraInput from '.';
import * as utils from '../../../util/AppHelpers';
import 'react-html5-camera-photo';
import { UploadToS3AndGetMediaPath } from '../UploadToS3AndGetMediaPath';

const prefix = 'CameraInput';
jest.mock('../../../util/AppHelpers', () => ({
    isInsideMobileApp: jest.fn(),
}));
jest.mock('../UploadToS3AndGetMediaPath'); // Mock the upload module

// Mocking inbuilt camera component's properties
const onCameraStartMock = jest.fn();
const onTakePhotoMock = jest.fn();
const onCameraStopMock = jest.fn();
const onCameraErrorMock = jest.fn();

// Dummy MediaStream & MediaRecorder for library "react-html5-camera-photo"
const mockStream = {
    getTracks: jest.fn(() => [
        {
            stop: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
        },
    ]),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
};

const mockRecorder = {
    _dataAvailableHandler: null,
    _inactiveHandler: null, // Add an internal handler for inactive event

    // Simulate the MediaRecorder instance
    stream: mockStream,

    addEventListener: jest.fn((event, handler) => {
        if (event === 'dataavailable') {
            mockRecorder._dataAvailableHandler = handler;
        } else if (event === 'inactive') {
            mockRecorder._inactiveHandler = handler;
        }
    }),

    start: jest.fn(() => {
        setTimeout(() => {
            if (mockRecorder._dataAvailableHandler) {
                const mockEvent = {
                    data: 'mock-captured-data', // Mock data that would be captured
                };
                mockRecorder._dataAvailableHandler(mockEvent);
            }
        }, 100);
    }),

    stop: jest.fn(() => {
        // Simulate the inactive event
        if (mockRecorder._inactiveHandler) {
            const inactiveEvent = new Event('inactive');
            mockRecorder._inactiveHandler(inactiveEvent);
        }
    }),
};

global.MediaRecorder = jest.fn(() => mockRecorder);

// Changing the behavior of camera component of react-html5-camera-photo
jest.mock('react-html5-camera-photo', () => {
    return jest.fn((props) => {
        // Simulate onCameraStart being called once on mount
        if (props.onCameraStart) {
            setTimeout(() => props.onCameraStart(mockStream), 0); // Pass the mock stream
        }
        // Mock implementation of the `recorder` object
        const mockRecorder = {
            _dataAvailableHandler: null, // Placeholder for dataavailable event handler
            addEventListener: jest.fn((event, handler) => {
                if (event === 'dataavailable') {
                    mockRecorder._dataAvailableHandler = handler;
                }
            }),
            start: jest.fn(() => {
                setTimeout(() => {
                    if (mockRecorder._dataAvailableHandler) {
                        const mockEvent = {
                            data: 'mock-captured-data', // Mock data that would be captured
                        };
                        mockRecorder._dataAvailableHandler(mockEvent);
                    }
                }, 100); // Simulate delay before data becomes available
            }),
            stop: jest.fn(),
        };

        // Give fake media stream(video) and capture
        global.MediaRecorder = jest.fn(() => mockRecorder);
        const handleClick = async () => {
            if (props.onTakePhoto) {
                const testImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAA';
                await props.onTakePhoto(testImageUrl);
            }

            // Simulate starting the recorder
            if (props.onCameraStart) {
                props.onCameraStart(mockStream);
            }
        };
        const handleStop = async () => {
            if (props.onCameraStop) {
                await props.onCameraStop();
            }
        };

        const handleError = () => {
            if (props.onCameraError) {
                props.onCameraError('mock-error');
            }
        };

        return (
            <div>
                <div className="react-html5-camera-photo">
                    <div id="container-circles">
                        <div id="outer-circle" onClick={handleClick}>
                            <div id="inner-circle" />
                        </div>
                    </div>
                </div>
            </div>
        );
    });
});

beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    // For inbuilt camera component which has css design to capture the photo
    Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
        value: jest.fn(() => ({
            drawImage: jest.fn(),
            fillRect: jest.fn(),
            clearRect: jest.fn(),
        })),
        configurable: true,
    });
    Object.defineProperty(HTMLCanvasElement.prototype, 'toDataURL', {
        value: jest.fn(() => 'data:image/png;base64,'),
        configurable: true,
    });

    // Mock HTMLMediaElement.prototype.play
    Object.defineProperty(HTMLMediaElement.prototype, 'play', {
        value: jest.fn(),
        configurable: true,
    });

    // Global level mocking for jsdom
    global.MediaRecorder = jest.fn().mockImplementation(() => {
        return {
            start: jest.fn(),
            stop: jest.fn(),
            requestData: jest.fn(),
            addEventListener: jest.fn((event, handler) => {
                if (event === 'dataavailable') {
                    // Save the handler to be called later in the test
                    this._dataAvailableHandler = handler;
                }
            }),
            removeEventListener: jest.fn(),
        };
    });
    // Mock MediaStream
    const mockMediaStream = {
        getTracks: jest.fn().mockReturnValue([
            {
                stop: jest.fn(),
                addEventListener: jest.fn(),
                removeEventListener: jest.fn(),
            },
        ]),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
    };

    // Mock getUserMedia
    global.navigator.mediaDevices = {
        getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
    };
    global.URL.createObjectURL = jest.fn(() => 'http://mock-url.com');

    // Mock for upload progress completed and received response from server
    UploadToS3AndGetMediaPath.mockImplementation(
        (url, fileName, progressCallback) => {
            return new Promise((resolve) => {
                // Directly simulate progress and final upload
                progressCallback(100, 'mockedUploadedUrl', true);
                resolve('mockedUploadedUrl');
            });
        }
    );
    // For Android
    process.env.REACT_USE_HTML5_CAMERA_FR_MOBILE_APP = 'true';
});

afterEach(() => {
    delete global.MediaRecorder;
    delete global.navigator.mediaDevices;
    HTMLCanvasElement.prototype.getContext.mockRestore();
    HTMLCanvasElement.prototype.toDataURL.mockRestore();
    HTMLMediaElement.prototype.play.mockRestore();
    global.URL.createObjectURL.mockRestore();
    jest.clearAllMocks();
});

test(`${prefix} smoke test`, async () => {
    utils.isInsideMobileApp.mockImplementation(() => true);
    render(<CameraInput />);
});

test(`${prefix}, should access camera and present recording for android`, async () => {
    utils.isInsideMobileApp.mockImplementation(() => true);
    const { container } = await waitFor(() => render(<CameraInput />));

    // // Turned on camera on button clicked
    const openCameraButton = screen.getByTestId('openCameraButton');
    expect(openCameraButton).toBeInTheDocument();

    // trigger event click
    await act(async () => {
        userEvent.click(openCameraButton);
    });

    expect(screen.getByText('Please wait ...')).toBeInTheDocument();
    const cameraView = container.querySelector('.react-html5-camera-photo');
    expect(cameraView).toBeInTheDocument();
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // capturing the image
    const captureButton = container.querySelector('#outer-circle');
    expect(captureButton).toBeInTheDocument();
    await act(async () => {
        userEvent.click(captureButton);
    });
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });
    // check img is captured or not
    await waitFor(() => {
        const capturedImage = screen.getByTestId('capturedImg');
        expect(capturedImage).toBeInTheDocument();
        expect(capturedImage.src).toMatch(/^data:image\/png;base64,/);
    });
});

test(`${prefix}, should captured the image from camera and save`, async () => {
    // Mocking isInsideMobileApp to return true
    utils.isInsideMobileApp.mockReturnValue(true);
    // methods gets called after saving an image
    const stopCapturingSpy = jest.spyOn(CameraInput.prototype, 'stopCapturing');
    const onNewCapturingSpy = jest.spyOn(
        CameraInput.prototype,
        'onNewCapturing'
    );
    const tellParentFilesChangedSpy = jest.spyOn(
        CameraInput.prototype,
        'tellParentFilesChanged'
    );
    const onCapturingCompletedMock = jest.fn();
    CameraInput.prototype.onCapturingCompleted = onCapturingCompletedMock;

    const { container } = await waitFor(() =>
        render(
            <CameraInput
                onCameraStart={onCameraStartMock}
                onTakePhoto={onTakePhotoMock}
                onCameraStop={onCameraStopMock}
                onCameraError={onCameraErrorMock}
            />
        )
    );

    // Turned on camera on button clicked
    const openCameraButton = screen.getByTestId('openCameraButton');
    expect(openCameraButton).toBeInTheDocument();
    await act(async () => {
        userEvent.click(openCameraButton);
    });

    // Check loader & Camera View
    expect(screen.getByText('Please wait ...')).toBeInTheDocument();
    const cameraView = container.querySelector('.react-html5-camera-photo');
    expect(cameraView).toBeInTheDocument();
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // Simulate capturing
    await act(async () => {
        const captureButton = container.querySelector('#outer-circle');
        expect(captureButton).toBeInTheDocument();
        userEvent.click(captureButton);
    });

    // check img is captured or not
    await waitFor(() => {
        const capturedImage = screen.getByTestId('capturedImg');
        expect(capturedImage).toBeInTheDocument();
        expect(capturedImage.src).toMatch(/^data:image\/png;base64,/);
    });
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // Simulate saving image
    const saveButton = screen.getByTestId('saveImage');
    await act(async () => {
        userEvent.click(saveButton);
    });
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // Ensure stop capturing was called
    await waitFor(() => {
        expect(stopCapturingSpy).toHaveBeenCalled();
    });

    // Upload image & gets its url
    const testImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAA';
    await waitFor(() => {
        expect(onNewCapturingSpy).toHaveBeenCalledWith(testImageUrl);
        expect(UploadToS3AndGetMediaPath).toHaveBeenCalledWith(
            testImageUrl, // url
            'capture_image.png', // fileName
            expect.any(Function) // progressCallback
        );
    });
    await waitFor(() => {
        expect(tellParentFilesChangedSpy).toHaveBeenCalled();
    });

    // Get uploaded images' preview
    const capturedPreview = screen.getByTestId('capturedPreview');
    expect(capturedPreview).toBeInTheDocument();

    // screen.debug();
});

test(`${prefix}, should retake the image from camera`, async () => {
    utils.isInsideMobileApp.mockImplementation(() => true);
    const onCameraStartMock = jest.fn();

    const { container } = await waitFor(() =>
        render(
            <CameraInput
                onCameraStart={onCameraStartMock}
                onTakePhoto={onTakePhotoMock}
                onCameraStop={onCameraStopMock}
                onCameraError={onCameraErrorMock}
            />
        )
    );

    // // Turned on camera on button clicked
    const openCameraButton = screen.getByTestId('openCameraButton');
    expect(openCameraButton).toBeInTheDocument();

    // trigger event click
    await act(async () => {
        userEvent.click(openCameraButton);
    });
    expect(screen.getByText('Please wait ...')).toBeInTheDocument();
    const cameraView = container.querySelector('.react-html5-camera-photo');
    expect(cameraView).toBeInTheDocument();
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // capturing the image and save
    const captureButton = container.querySelector('#outer-circle');
    expect(captureButton).toBeInTheDocument();
    await act(async () => {
        userEvent.click(captureButton);
    });
    waitFor(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
    });

    // check captured image
    await waitFor(() => {
        const capturedImage = screen.getByTestId('capturedImg');
        expect(capturedImage).toBeInTheDocument();
        expect(capturedImage.src).toMatch(/^data:image\/png;base64,/);
    });

    // retake image
    const retakeButton = screen.getByText('Retake');
    await act(async () => {
        userEvent.click(retakeButton);
    });

    // check camera is present or not
    const cameraView2 = container.querySelector('.react-html5-camera-photo');
    expect(cameraView2).toBeInTheDocument();
});
